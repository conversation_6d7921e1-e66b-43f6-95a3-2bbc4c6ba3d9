import type { Meta, StoryObj } from '@storybook/react-vite'
import { action } from 'storybook/actions'
import {
  InputEmail,
  type InputEmailProps,
} from '@thrift/design-system/packages/molecules/InputEmail'

const meta = {
  title: 'Molecules/InputEmail',
  component: InputEmail,
  argTypes: {
    label: { control: 'text' },
  },
} satisfies Meta<InputEmailProps>

export default meta

type Story = StoryObj<InputEmailProps>

export const InputEmailDefault: Story = {
  name: 'InputEmail',
  args: {
    label: 'Name',
    name: 'test',
    value: '',
    onChange: action('onChange'),
    onBlur: action('onBlur'),
    error: '',
  },
}

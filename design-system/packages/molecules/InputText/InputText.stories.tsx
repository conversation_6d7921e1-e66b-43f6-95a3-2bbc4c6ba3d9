import type { Meta, StoryObj } from '@storybook/react-vite'
import { action } from 'storybook/actions'
import {
  InputText,
  type InputTextProps,
} from '@thrift/design-system/packages/molecules/InputText'

const meta = {
  title: 'Molecules/InputText',
  component: InputText,
  argTypes: {
    label: { control: 'text' },
  },
} satisfies Meta<InputTextProps>

export default meta

type Story = StoryObj<InputTextProps>

export const InputTextDefault: Story = {
  name: 'InputText',
  args: {
    label: 'Name',
    name: 'test',
    value: '',
    onChange: action('onChange'),
    onBlur: action('onBlur'),
    error: '',
  },
}

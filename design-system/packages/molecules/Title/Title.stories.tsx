import type { Meta, StoryObj } from '@storybook/react-vite'
import { faker } from '@faker-js/faker'
import {
  Title,
  type TitleProps,
} from '@thrift/design-system/packages/molecules/Title'

const meta = {
  title: 'Molecules/Title',
  component: Title,
  argTypes: {
    children: { control: 'text' },
  },
} satisfies Meta<TitleProps>

export default meta

type Story = StoryObj<TitleProps>

export const TitleDefault: Story = {
  name: 'Title',
  args: {
    children: faker.company.name(),
  },
}

import type { Column } from '@thrift/design-system/packages/molecules/Table/Table.types'
import { Table } from '@thrift/design-system/packages/molecules/Table/Table.component'

type Personal = {
  id: number
  nome: string
  idade: number
}

const columns: Column<Personal>[] = [
  { header: 'ID', accessor: 'id' },
  { header: 'Nome', accessor: 'nome' },
  { header: 'Idade', accessor: 'idade' },
]

const data: Personal[] = [
  { id: 1, nome: 'Alice', idade: 28 },
  { id: 2, nome: '<PERSON>', idade: 35 },
]

export default {
  title: 'Molecules/Table',
  component: Table,
}

export const Default = () => (
  <Table<Personal> title="Personals" columns={columns} data={data} />
)

export const Loading = () => (
  <Table<Personal>
    title="Personals"
    columns={columns}
    data={[]}
    isLoading
    loadingComponent="Carregando"
  />
)

export const Error = () => (
  <Table<Personal>
    title="Personals"
    columns={columns}
    data={[]}
    isError
    errorComponent="Erro ao buscar dados"
  />
)

import type { TableProps } from '@thrift/design-system/packages/molecules/Table/Table.types'

export function Table<T>({
  title,
  columns,
  data,
  isLoading,
  isError,
  errorComponent,
  emptyComponent,
  loadingComponent,
}: TableProps<T>) {
  const renderBody = () => {
    if (isLoading) {
      return loadingComponent
    }
    if (isError) {
      return errorComponent
    }
    if (!data || data?.length === 0) {
      return emptyComponent
    }

    return data.map((row, idx) => (
      <tr key={idx} className="hover:bg-gray-50">
        {columns.map((col) => (
          <td
            key={String(col.accessor)}
            className="px-4 py-2 border-b border-gray-200 whitespace-nowrap"
          >
            {String(row[col.accessor])}
          </td>
        ))}
      </tr>
    ))
  }

  return (
    <div className="flex flex-col gap-4">
      {title && title}
      <div className="overflow-x-auto w-full border border-gray-300 rounded-md">
        <table className="min-w-full table-auto">
          <thead className="bg-gray-100">
            <tr>
              {columns.map((col) => (
                <th
                  key={String(col.accessor)}
                  className="text-left px-4 py-2 border-b border-gray-300"
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>{renderBody()}</tbody>
        </table>
      </div>
    </div>
  )
}

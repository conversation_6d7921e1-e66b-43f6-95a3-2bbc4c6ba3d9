import { tv, type VariantProps } from 'tailwind-variants'

export const toastVariants = tv({
  base: 'p-4 w-2xl rounded shadow text-white animate-slide-in-right gap-3',
  variants: {
    type: {
      success: 'bg-green',
      error: 'bg-red',
      info: 'bg-blue',
      warning: 'bg-yellow',
    },
  },
  defaultVariants: {
    type: 'success',
  },
})

export type ToastVariantProps = VariantProps<typeof toastVariants>

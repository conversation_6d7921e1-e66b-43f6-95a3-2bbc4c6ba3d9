import type { Meta, StoryObj } from '@storybook/react-vite'
import {
  ToastProvider,
  useToast,
} from '@thrift/design-system/packages/molecules/Toast'

const meta: Meta = {
  title: 'Molecules/Toast',
  component: () => null,
}

export default meta

type Story = StoryObj

export const Default: Story = {
  render: () => {
    const Demo = () => {
      const { showToast } = useToast()

      return (
        <div className="p-8 bg-gray-100 flex gap-4">
          <button
            onClick={() =>
              showToast({
                title: 'Sucesso!',
                message: 'Sucesso!',
                type: 'success',
              })
            }
            className="bg-green text-white px-4 py-2 rounded"
          >
            Sucesso
          </button>
          <button
            onClick={() =>
              showToast({ title: 'Erro!', message: 'Erro!', type: 'error' })
            }
            className="bg-red text-white px-4 py-2 rounded"
          >
            Erro
          </button>
          <button
            onClick={() =>
              showToast({ title: 'Info!', message: 'Info!', type: 'info' })
            }
            className="bg-blue text-white px-4 py-2 rounded"
          >
            Info
          </button>
          <button
            onClick={() =>
              showToast({
                title: 'Warning!',
                message: 'Warning!',
                type: 'warning',
              })
            }
            className="bg-yellow text-white px-4 py-2 rounded"
          >
            Warning
          </button>
        </div>
      )
    }

    return (
      <ToastProvider>
        <Demo />
      </ToastProvider>
    )
  },
}

import { useState } from 'react'
import {
  ToastContext,
  type ToastProps,
} from '@thrift/design-system/packages/molecules/Toast'
import { toastVariants } from '@thrift/design-system/packages/molecules/Toast/Toast.variants'

export const ToastProvider = ({ children }: { children: React.ReactNode }) => {
  const [toasts, setToasts] = useState<ToastProps[]>([])

  const showToast = (toast: Omit<ToastProps, 'id'>) => {
    const id = Date.now().toString()

    setToasts((prev) => [...prev, { id, ...toast }])

    setTimeout(() => {
      setToasts((prev) => prev.filter((t) => t.id !== id))
    }, 3000)
  }

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-4">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={toastVariants({
              type: toast.type,
            })}
          >
            {toast.title && (
              <span className="text-small font-bold">{toast.title}</span>
            )}
            {toast.message && (
              <p className="text-tiny font-semibold">{toast.message}</p>
            )}
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  )
}

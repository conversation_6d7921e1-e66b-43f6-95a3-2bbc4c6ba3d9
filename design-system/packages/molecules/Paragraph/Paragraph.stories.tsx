import type { Meta, StoryObj } from '@storybook/react-vite'
import { faker } from '@faker-js/faker'
import {
  Paragraph,
  type ParagraphProps,
} from '@thrift/design-system/packages/molecules/Paragraph'

const meta = {
  title: 'Molecules/Paragraph',
  component: Paragraph,
  argTypes: {
    children: { control: 'text' },
  },
} satisfies Meta<ParagraphProps>

export default meta

type Story = StoryObj<ParagraphProps>

export const ParagraphDefault: Story = {
  name: 'Paragraph',
  args: {
    children: faker.lorem.paragraph(),
  },
}

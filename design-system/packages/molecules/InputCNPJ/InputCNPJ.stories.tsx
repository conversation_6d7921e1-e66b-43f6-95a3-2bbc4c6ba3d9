import type { Meta, StoryObj } from '@storybook/react-vite'
import { action } from 'storybook/actions'
import {
  InputCNPJ,
  type InputCNPJProps,
} from '@thrift/design-system/packages/molecules/InputCNPJ'

const meta = {
  title: 'Molecules/InputCNPJ',
  component: InputCNPJ,
  argTypes: {
    label: { control: 'text' },
  },
} satisfies Meta<InputCNPJProps>

export default meta

type Story = StoryObj<InputCNPJProps>

export const InputCNPJDefault: Story = {
  name: 'InputCNPJ',
  args: {
    label: 'CNPJ',
    name: 'cnpj',
    value: '',
    onChange: action('onChange'),
    onBlur: action('onBlur'),
    error: '',
  },
}

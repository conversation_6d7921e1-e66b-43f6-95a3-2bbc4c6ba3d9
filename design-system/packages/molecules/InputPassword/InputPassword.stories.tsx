import type { Meta, StoryObj } from '@storybook/react-vite'
import { action } from 'storybook/actions'
import {
  InputPassword,
  type InputPasswordProps,
} from '@thrift/design-system/packages/molecules/InputPassword'

const meta = {
  title: 'Molecules/InputPassword',
  component: InputPassword,
  argTypes: {
    label: { control: 'text' },
  },
} satisfies Meta<InputPasswordProps>

export default meta

type Story = StoryObj<InputPasswordProps>

export const InputPasswordDefault: Story = {
  name: 'InputPassword',
  args: {
    label: 'Name',
    name: 'test',
    value: '',
    onChange: action('onChange'),
    onBlur: action('onBlur'),
    error: '',
  },
}

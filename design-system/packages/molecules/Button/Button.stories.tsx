import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import {
  Button,
  type ButtonProps,
} from '@thrift/design-system/packages/molecules/Button'

const meta = {
  title: 'Molecules/Button',
  component: Button,
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'tertiary'],
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    onClick: { action: 'clicked' },
  },
} satisfies Meta<ButtonProps>

export default meta

type Story = StoryObj<ButtonProps>

export const Primary: Story = {
  args: {
    children: 'Enviar',
    variant: 'primary',
    size: 'lg',
    disabled: false,
  },
  render: (args) => (
    <div className="w-100">
      <Button {...args} />
    </div>
  ),
}

export const Secondary: Story = {
  args: {
    children: 'Enviar',
    variant: 'secondary',
    size: 'lg',
    disabled: false,
  },
  render: (args) => (
    <div className="w-100">
      <Button {...args} />
    </div>
  ),
}

export const Ghost: Story = {
  args: {
    children: 'Enviar',
    variant: 'ghost',
    size: 'lg',
    disabled: false,
  },
  render: (args) => (
    <div className="w-100">
      <Button {...args} />
    </div>
  ),
}

export const Cancel: Story = {
  args: {
    children: 'Cancelar',
    variant: 'cancel',
    size: 'lg',
    disabled: false,
  },
  render: (args) => (
    <div className="w-100">
      <Button {...args} />
    </div>
  ),
}

export const Disabled: Story = {
  args: {
    children: 'Desativado',
    variant: 'primary',
    size: 'lg',
    disabled: true,
  },
  render: (args) => (
    <div className="w-100">
      <Button {...args} />
    </div>
  ),
}

export const Loading: Story = {
  args: {
    children: 'Salvando...',
    variant: 'primary',
    size: 'lg',
    loading: true,
    disabled: false,
  },
  render: (args) => (
    <div className="w-100">
      <Button {...args} />
    </div>
  ),
}

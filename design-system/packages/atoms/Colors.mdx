import { Meta, ColorPalette, ColorItem, Primary } from "@storybook/addon-docs/blocks"

<Meta title="Atoms/Colors" />

# Colors

<ColorPalette>
  <ColorItem
    title="Back and White"
    colors={{
      White: "var(--color-white)",
      Black: "var(--color-black)",
    }}
  />
  <ColorItem
    title="Gray Scale"
    colors={{
      '100': "var(--color-gray-100)",
      '200': "var(--color-gray-200)",
      '300': "var(--color-gray-300)",
      '400': "var(--color-gray-400)",
      '500': "var(--color-gray-500)",
      '600': "var(--color-gray-600)",
      '700': "var(--color-gray-700)",
      '900': "var(--color-gray-900)",
    }}
  />
  <ColorItem
    title="Blue Colors"
    colors={{
      Light: "var(--color-blue-light)",
      Default: "var(--color-blue)",
      Dark: "var(--color-blue-dark)",
    }}
  />
  <ColorItem
    title="Green Colors"
    colors={{
      Light: "var(--color-green-light)",
      Default: "var(--color-green)",
      Dark: "var(--color-green-dark)",
    }}
  />
  <ColorItem
    title="Yellow Colors"
    colors={{
      Light: "var(--color-yellow-light)",
      Default: "var(--color-yellow)",
      Dark: "var(--color-yellow-dark)",
    }}
  />
  <ColorItem
    title="Red Colors"
    colors={{
      Light: "var(--color-red-light)",
      Default: "var(--color-red)",
      Dark: "var(--color-red-dark)",
    }}
  />
  <ColorItem
    title="Brand Colors"
    colors={{
      Primary: "var(--color-primary)",
      Secondary: "var(--color-secondary)",
      Tertiary: "var(--color-tertiary)",
    }}
  />
  <ColorItem
    title="Background"
    colors={{
      Light: "var(--color-backgroud-light)"
    }}
  />
</ColorPalette>

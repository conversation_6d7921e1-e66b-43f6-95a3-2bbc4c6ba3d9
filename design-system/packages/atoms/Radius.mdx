import classNames from 'classnames'
import { Meta, Typeset, Story } from '@storybook/addon-docs/blocks'

export const BoxRadius = ({ name }) => {
  const rounded = {
    tiny: 'rounded-tiny',
    small: 'rounded-small',
    medium: 'rounded-medium',
    large: 'rounded-large',
    huge: 'rounded-huge',
  }

  return (
    <article>
      <p>rounded-{name}</p>
      <div className={classNames('size-32 bg-tertiary', { [rounded[name]]: true})} />
    </article>
  )
}

<Meta title="Atoms/Radius" />

## Radius


<section>
  <BoxRadius name="tiny" />
  <BoxRadius name="small" />
  <BoxRadius name="medium" />
  <BoxRadius name="large" />
  <BoxRadius name="huge" />
</section>

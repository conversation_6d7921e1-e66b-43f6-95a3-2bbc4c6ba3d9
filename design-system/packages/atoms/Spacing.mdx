import classNames from 'classnames'
import { Meta, Typeset, Story } from '@storybook/addon-docs/blocks'

export const BoxSpacing = ({ name }) => {
  const size = {
    tiny: 'gap-x-tiny',
    small: 'gap-x-small',
    medium: 'gap-x-medium',
    large: 'gap-x-large',
    huge: 'gap-x-huge',
  }

  return (
    <article>
      <p>{name}</p>
      <section className={classNames('flex flex-row', { [size[name]]: true})}>
        <div className="size-32 bg-gray-100" />
        <div className="size-32 bg-tertiary" />
        <div className="size-32 bg-gray-100" />
      </section>
    </article>
  )
}

<Meta title="Atoms/Spacing" />

## Spacing

<section>
<BoxSpacing name="tiny" />
<BoxSpacing name="small" />
<BoxSpacing name="medium" />
<BoxSpacing name="large" />
<BoxSpacing name="huge" />
</section>

import { Meta, Typeset, Story } from '@storybook/addon-docs/blocks'
import { faker } from '@faker-js/faker'

<Meta title="Atoms/Sizing" />

## Sizing

export const params = {
  fontSizes:[
    'text-tiny',
    'text-small',
    'text-medium',
    'text-large',
    'text-huge',
  ],
  fontWeight: 300,
  sampleText: faker.lorem.words(5),
  fontFamily: "'Host Grotesk', sans-serif"
}

<Typeset {...params} />

### Bold
<Typeset {...params} fontWeight="800" />

### Italic
<i><Typeset {...params} /></i>

### Bold and Italic
<i><Typeset {...params} fontWeight="800" /></i>

### Bold, Italic, Underline
<u><i><Typeset {...params} fontWeight="800" /></i></u>

import React from 'react'
import type { Preview } from '@storybook/react-vite'
import '@thrift/design-system/packages/styles/globals.css'

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      values: [{ name: 'Light', value: 'var(--color-backgroud-light)' }],
      default: 'Light',
    },
    parameters: { layout: 'fullscreen' },
  },
  decorators: [
    (Story) => (
      <div style={{ margin: '1rem', height: 'calc(100vh - 4rem)' }}>
        <Story />
      </div>
    ),
  ],
}

export default preview

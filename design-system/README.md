# Thrift Design System
Thrift set of standards, documentation, and reusable components that guide the development

## About the Packages (`packages`)

My approach here (_Application Architecture_) was using the _[Atomic Design Methodology](https://atomicdesign.bradfrost.com/chapter-2/)_ as the base of the application, where:

- Read about _[atoms, molecules, organisms, templates, and pages](https://atomicdesign.bradfrost.com/chapter-2/)_ packages;
- _[app.config.json](app.config.json) has the basic parameters to run the application. Just see the file; no doubt you will understand.

## How to use this Application

### Github Personal Access Token
[Creating a personal access token (classic)](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic) in the [github](https://github.com/).
- **Note:** `personal-token-github`
- **Expiration:** `No expiration`
- **Select scopes:** `notifications`, `read:packages`, `repo`, and `workflow`

After that, inside in the directory repository, execute the command below:
```bash
$ export PERSONAL_TOKEN_GITHUB=<<personal-token-github>>
$ git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
```
*Replace the `<<personal-token-github>>` to your Github Personal Access Token*

## Commands

The _[package.json](./package.json)_ in the `scripts` item you can see all app commands.

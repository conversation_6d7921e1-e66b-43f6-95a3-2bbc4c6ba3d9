{"name": "@thrift/design-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "yarn storybook", "build": "yarn healthcheck && tsc -b && vite build", "lint": "eslint .", "lint:fix": "yarn lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "typecheck": "tsc --noEmit", "healthcheck": "yarn lint && yarn format && yarn typecheck", "preview": "vite preview", "storybook": "yarn healthcheck && storybook dev -p 6006 --no-open", "build-storybook": "yarn healthcheck && storybook build", "postinstall": "flowbite-react patch", "prepare": "husky"}, "peerDependencies": {"classnames": "2.5.1", "flowbite-react": "0.11.7", "lucide-react": "0.510.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "4.1.5"}, "dependencies": {"classnames": "2.5.1", "flowbite-react": "0.11.7", "react": "19.1.0", "react-dom": "19.1.0", "react-number-format": "5.4.4", "tailwind-variants": "1.0.0", "tailwindcss": "4.1.8"}, "devDependencies": {"@chromatic-com/storybook": "4.0.0", "@eslint/js": "9.27.0", "@faker-js/faker": "9.8.0", "@storybook/addon-docs": "9.0.0", "@storybook/addon-onboarding": "9.0.0", "@storybook/react-vite": "9.0.0", "@tailwindcss/vite": "4.1.8", "@types/node": "22.15.23", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@vitejs/plugin-react": "4.5.0", "@vitest/browser": "3.1.4", "@vitest/coverage-v8": "3.1.4", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-react-hooks": "6.0.0", "eslint-plugin-react-refresh": "0.4.20", "eslint-plugin-storybook": "9.0.0", "globals": "16.2.0", "husky": "9.1.7", "lucide-react": "0.511.0", "prettier": "3.5.3", "storybook": "9.0.0", "typescript": "5.8.3", "typescript-eslint": "8.33.0", "vite": "6.3.5", "vitest": "3.1.4", "zod": "3.25.32"}, "engines": {"node": ">=20 <24", "npm": ">=10 <12", "yarn": ">=1.22 <2"}}
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

import pt from '@app/domain/locales/i18n/pt'

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    debug: false,
    lowerCaseLng: true,
    resources: {
      pt,
    },
    fallbackLng: 'pt',
    interpolation: {
      escapeValue: false,
    },
  })

export default i18n

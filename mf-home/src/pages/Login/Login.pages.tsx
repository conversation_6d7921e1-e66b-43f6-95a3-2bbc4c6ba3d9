import { LoginWrap } from '@thrift/design-system/packages/templates/Login'
import { SignIn } from '@thrift/design-system/packages/organisms/SignIn'
import { SignInBrandInfo } from '@thrift/design-system/packages/organisms/SignInBrandInfo'
import { Title } from '@thrift/design-system/packages/molecules/Title'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { Paragraph } from '@thrift/design-system/packages/molecules/Paragraph'
import { InputPassword } from '@thrift/design-system/packages/molecules/InputPassword'
import { InputEmail } from '@thrift/design-system/packages/molecules/InputEmail'
import { useLogin } from '@app/application/Login'
import { useLanguage } from '@app/application/Language'

export const Login = () => {
  const { handleSubmit, register, errors, loading } = useLogin()
  const { translate } = useLanguage()

  return (
    <LoginWrap>
      <SignInBrandInfo />
      <SignIn>
        <Title>Thrift</Title>
        <Paragraph>{translate('login:description')}</Paragraph>
        <InputEmail
          {...register('username')}
          label={translate('login:username')}
          error={errors.username?.message}
        />
        <InputPassword
          {...register('password')}
          label={translate('login:password')}
          error={errors.password?.message}
        />
        <Button type="button" onClick={handleSubmit} loading={loading}>
          {translate('login:button')}
        </Button>
      </SignIn>
    </LoginWrap>
  )
}

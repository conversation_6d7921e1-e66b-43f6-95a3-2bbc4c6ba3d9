import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  type LoginSchemaType,
  useLoginSchema,
} from '@app/application/Login/Login.schemas'
import { useLoginMutation } from '@app/domain/Login'
import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'

export const useLogin = () => {
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const LoginSchema = useLoginSchema()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  })

  const { postLoginAsync, loading } = useLoginMutation()

  const onSubmit = async (data: LoginSchemaType) => {
    try {
      await postLoginAsync(data)
      // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    } catch (e) {
      showToast({
        title: translate('login:loginFailedTitle'),
        message: translate('login:loginFailedMessage'),
        type: 'error',
      })
    }
  }

  return {
    handleSubmit: handleSubmit(onSubmit),
    register,
    errors,
    loading,
  }
}

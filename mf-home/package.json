{"name": "@thrift/mf-home", "version": "0.1.0", "private": true, "description": "Home web page & application", "author": "Thrift Technology <<EMAIL>>", "type": "module", "repository": {"type": "git", "url": "https://github.com/thrift-technology/mf-home.git"}, "scripts": {"dev": "yarn healthcheck && vite", "build": "yarn healthcheck && tsc -b && vite build", "lint": "eslint .", "lint:fix": "yarn lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "typecheck": "tsc --noEmit", "healthcheck": "yarn lint && yarn format && yarn typecheck", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@tanstack/react-query": "5.77.2", "@thrift/design-system": "https://github.com/thrift-technology/design-system.git#main", "axios": "1.9.0", "i18next": "25.2.1", "i18next-browser-languagedetector": "8.1.0", "lucide-react": "0.511.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.4", "react-router-dom": "7.6.1", "react-i18next": "15.5.2", "zod": "3.25.32"}, "devDependencies": {"@eslint/js": "9.27.0", "@tailwindcss/vite": "4.1.8", "@types/node": "22.15.24", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@vitejs/plugin-react": "4.5.0", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-react-hooks": "6.0.0", "eslint-plugin-react-refresh": "0.4.20", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.2.0", "prettier": "3.5.3", "tailwindcss": "4.1.8", "typescript": "5.8.3", "typescript-eslint": "8.33.0", "vite": "6.3.5"}}
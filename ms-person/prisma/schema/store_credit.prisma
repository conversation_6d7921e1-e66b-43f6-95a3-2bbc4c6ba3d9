model StoreCredit {
  id                       String   @id @default(uuid())
  person                   Person   @relation(fields: [personId], references: [id])
  personId                 String
  balance                  Decimal  @default(0)
  currency                 String   @default("BRL")
  issuingStoreLegalPersonId String  // ID of the PersonLegal entity representing the franchise store
  originatingProposalId    String   // ID of the EvaluationProposal from ms-evaluation for traceability
  notes                    String?
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
  deleted                  Boolean  @default(false)

  @@map("store_credits")
}

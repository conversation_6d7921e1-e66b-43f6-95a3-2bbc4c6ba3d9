# Thrift Purchase Application
Purchase application service

## About the Source (`src`) Package

My approach here (_Application Architecture_) was using the _[Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)_ as the base of the application, where:

- _[domain](src/domain)_ package represents the **Enterprise Business Rules**;
- _[application](src/application)_ package represents the **Application Business Rules**;
- _[resources](src/resources)_ package represents the **Application Business Rules**;
- _[app.config.json](app.config.sample.json) (copy/paste file)_ has the basic parameters to run the application. Just see the file; no doubt you will understand.

## About the Other Packages

- _[eslint.config.js](eslint.config.js)_ configuration to _[eslint](https://eslint.org)_;
- _[.swagger](.swagger)_ configuration to [swagger](https://swagger.js.org);
- _[.jest](.jest)_ configuration to [jest](https://jest.dev).
- _[.vscode](.vscode)_ workspace configuration to [vscode](https://code.visualstudio.com/docs/editor/workspaces#_singlefolder-workspace-settings).

## How to use this Application

### Github Personal Access Token
[Creating a personal access token (classic)](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic) in the [github](https://github.com/).
- **Note:** `personal-token-github`
- **Expiration:** `No expiration`
- **Select scopes:** `notifications`, `read:packages`, `repo`, and `workflow`

After that, inside in the directory repository, execute the command below:
```bash
$ export PERSONAL_TOKEN_GITHUB=<<personal-token-github>>
$ git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
```
*Replace the `<<personal-token-github>>` to your Github Personal Access Token*

### Docker
You will need [docker](https://docs.docker.com/engine/install) and [docker-compose](https://docs.docker.com/compose/install) to use the commands below. It's necessary to access the repository root path.

## Commands

Below some app commands.

### provisioning

[Provisioning](https://en.wikipedia.org/wiki/Provisioning_(technology)) is the process which involves the automated setup of infrastructure, including servers, networks, and storage, alongside the deployment and configuration of the macro/micro-services themselves, ensuring a scalable and secure environment."

```bash
$ docker-compose run --rm provisioning
```

### database

Start database service.

```bash
$ docker-compose run --rm database
```

### stop

Stop all services.

```bash
$ docker-compose down --volumes
```

### lint

[Lint](<https://en.wikipedia.org/wiki/Lint_(software)>) is the computer science term for a static code analysis tool used to flag programming errors, bugs, stylistic errors and suspicious constructs.

```bash
$ docker-compose run --rm lint
```

### Format

[Formart](https://en.wikipedia.org/wiki/Coding_conventions) (Coding Conventions) are a set of guidelines for a specific programming language that recommend programming style, practices, and methods for each aspect of a program written in that language..

```bash
$ docker-compose run --rm format
```

### build

[Build](https://en.wikipedia.org/wiki/Software_build) is the process of converting source code files into standalone software artifact(s) that can be run on a computer, or the result of doing so.

```bash
$ docker-compose run --rm build
```

import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { Domain } from '@app/domain/wallet/Domain/Domain'
import {
  postDomain,
  putDomain,
  getDomains,
  getDomainById,
  deleteDomain,
} from '@app/domain/wallet/Domain/Domain.api'

export const useGetDomainsQuery = () => {
  const getDomainsFn = useQuery({
    queryKey: ['getDomains'],
    queryFn: () => getDomains(),
  })

  return getDomainsFn
}

export const useGetDomainByIdQuery = (id: string) => {
  const getDomainByIdFn = useQuery({
    queryKey: ['getDomainById', id],
    queryFn: () => getDomainById(id),
    enabled: id !== null,
  })

  return getDomainByIdFn
}

export const usePostDomainMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Domain) => postDomain(params),
  })

  const postDomainAsync = useCallback(
    async (props: Domain) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postDomainAsync,
    loading: isPending,
  }
}

export const usePutDomainMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Domain) => putDomain(params),
  })

  const putDomainAsync = useCallback(
    async (props: Domain) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putDomainAsync,
    loading: isPending,
  }
}

export const useDeleteDomainMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteDomain(id),
  })

  const deleteDomainAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteDomainAsync,
    loading: isPending,
  }
}

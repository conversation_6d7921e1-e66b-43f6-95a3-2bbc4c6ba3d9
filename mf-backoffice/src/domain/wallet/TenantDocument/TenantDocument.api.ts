import api from '@app/domain/services/api'
import type {
  TenantDocument,
  TenantDocumentListParams,
} from '@app/domain/wallet/TenantDocument/TenantDocument'

export const getTenantDocuments = async (params: TenantDocumentListParams) => {
  const response = await api.get(
    `/wallet/tenant-documents?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getTenantDocumentById = async (id: string) => {
  const response = await api.get(`/wallet/tenant-documents/${id}`)

  return response?.data
}

export const postTenantDocument = async (
  params: Omit<TenantDocument, 'id'>,
) => {
  const response = await api.post('/wallet/tenant-documents', params)

  return response?.data
}

export const putTenantDocument = async (params: TenantDocument) => {
  const { id, ...body } = params
  const response = await api.put(`/wallet/tenant-documents/${id}`, body)

  return response?.data
}

export const deleteTenantDocument = async (id: string) => {
  const response = await api.delete(`/wallet/tenant-documents/${id}`)

  return response?.data
}

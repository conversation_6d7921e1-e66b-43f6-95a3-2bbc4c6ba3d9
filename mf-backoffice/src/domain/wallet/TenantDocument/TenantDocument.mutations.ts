import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  TenantDocumentListParams,
  TenantDocument,
} from '@app/domain/wallet/TenantDocument/TenantDocument'
import {
  postTenantDocument,
  putTenantDocument,
  getTenantDocuments,
  getTenantDocumentById,
  deleteTenantDocument,
} from '@app/domain/wallet/TenantDocument/TenantDocument.api'

export const useGetTenantDocumentsQuery = (
  params: TenantDocumentListParams,
) => {
  const getTenantDocumentsFn = useQuery({
    queryKey: ['getTenantDocuments', params],
    queryFn: () => getTenantDocuments(params),
    enabled: params !== null,
  })

  return getTenantDocumentsFn
}

export const useGetTenantDocumentByIdQuery = (id: string) => {
  const getTenantDocumentByIdFn = useQuery({
    queryKey: ['getTenantDocumentById', id],
    queryFn: () => getTenantDocumentById(id),
    enabled: id !== null,
  })

  return getTenantDocumentByIdFn
}

export const usePostTenantDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: TenantDocument) => postTenantDocument(params),
  })

  const postTenantDocumentAsync = useCallback(
    async (props: TenantDocument) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postTenantDocumentAsync,
    loading: isPending,
  }
}

export const usePutTenantDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: TenantDocument) => putTenantDocument(params),
  })

  const putTenantDocumentAsync = useCallback(
    async (props: TenantDocument) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putTenantDocumentAsync,
    loading: isPending,
  }
}

export const useDeleteTenantDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteTenantDocument(id),
  })

  const deleteTenantDocumentAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteTenantDocumentAsync,
    loading: isPending,
  }
}

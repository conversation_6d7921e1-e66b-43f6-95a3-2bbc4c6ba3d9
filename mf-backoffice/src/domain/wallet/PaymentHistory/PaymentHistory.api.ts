import api from '@app/domain/services/api'
import type {
  PaymentHistory,
  PaymentHistoryListParams,
} from '@app/domain/wallet/PaymentHistory/PaymentHistory'

export const getPaymentHistories = async (params: PaymentHistoryListParams) => {
  const response = await api.get(
    `/wallet/payment-history?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getPaymentHistoryById = async (id: string) => {
  const response = await api.get(`/wallet/payment-history/${id}`)

  return response?.data
}

export const postPaymentHistory = async (
  params: Omit<PaymentHistory, 'id'>,
) => {
  const response = await api.post('/wallet/payment-history', params)

  return response?.data
}

export const putPaymentHistory = async (params: PaymentHistory) => {
  const { id, ...body } = params
  const response = await api.put(`/wallet/payment-history/${id}`, body)

  return response?.data
}

export const deletePaymentHistory = async (id: string) => {
  const response = await api.delete(`/wallet/payment-history/${id}`)

  return response?.data
}

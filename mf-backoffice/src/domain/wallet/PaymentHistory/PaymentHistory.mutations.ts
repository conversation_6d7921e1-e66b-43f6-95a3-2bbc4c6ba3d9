import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  PaymentHistoryListParams,
  PaymentHistory,
} from '@app/domain/wallet/PaymentHistory/PaymentHistory'
import {
  postPaymentHistory,
  putPaymentHistory,
  getPaymentHistories,
  getPaymentHistoryById,
  deletePaymentHistory,
} from '@app/domain/wallet/PaymentHistory/PaymentHistory.api'

export const useGetPaymentHistoriesQuery = (
  params: PaymentHistoryListParams,
) => {
  const getPaymentHistoriesFn = useQuery({
    queryKey: ['getPaymentHistories', params],
    queryFn: () => getPaymentHistories(params),
    enabled: params !== null,
  })

  return getPaymentHistoriesFn
}

export const useGetPaymentHistoryByIdQuery = (id: string) => {
  const getPaymentHistoryByIdFn = useQuery({
    queryKey: ['getPaymentHistoryById', id],
    queryFn: () => getPaymentHistoryById(id),
    enabled: id !== null,
  })

  return getPaymentHistoryByIdFn
}

export const usePostPaymentHistoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PaymentHistory) => postPaymentHistory(params),
  })

  const postPaymentHistoryAsync = useCallback(
    async (props: PaymentHistory) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postPaymentHistoryAsync,
    loading: isPending,
  }
}

export const usePutPaymentHistoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PaymentHistory) => putPaymentHistory(params),
  })

  const putPaymentHistoryAsync = useCallback(
    async (props: PaymentHistory) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putPaymentHistoryAsync,
    loading: isPending,
  }
}

export const useDeletePaymentHistoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deletePaymentHistory(id),
  })

  const deletePaymentHistoryAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deletePaymentHistoryAsync,
    loading: isPending,
  }
}

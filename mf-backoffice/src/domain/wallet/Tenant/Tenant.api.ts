import api from '@app/domain/services/api'
import type { Tenant, TenantListParams } from '@app/domain/wallet/Tenant/Tenant'

export const getTenants = async (params: TenantListParams) => {
  const response = await api.get(
    `/wallet/tenants?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getTenantById = async (id: string) => {
  const response = await api.get(`/wallet/tenants/${id}`)

  return response?.data
}

export const postTenant = async (params: Omit<Tenant, 'id'>) => {
  const response = await api.post('/wallet/tenants', params)

  return response?.data
}

export const putTenant = async (params: Tenant) => {
  const { id, ...body } = params
  const response = await api.put(`/wallet/tenants/${id}`, body)

  return response?.data
}

export const deleteTenant = async (id: string) => {
  const response = await api.delete(`/wallet/tenants/${id}`)

  return response?.data
}

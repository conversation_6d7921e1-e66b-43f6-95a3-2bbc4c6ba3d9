import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { TenantListParams, Tenant } from '@app/domain/wallet/Tenant/Tenant'
import {
  postTenant,
  putTenant,
  getTenants,
  getTenantById,
  deleteTenant,
} from '@app/domain/wallet/Tenant/Tenant.api'

export const useGetTenantsQuery = (params: TenantListParams) => {
  const getTenantsFn = useQuery({
    queryKey: ['getTenants', params],
    queryFn: () => getTenants(params),
    enabled: params !== null,
  })

  return getTenantsFn
}

export const useGetTenantByIdQuery = (id: string) => {
  const getTenantByIdFn = useQuery({
    queryKey: ['getTenantById', id],
    queryFn: () => getTenantById(id),
    enabled: id !== null,
  })

  return getTenantByIdFn
}

export const usePostTenantMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Tenant) => postTenant(params),
  })

  const postTenantAsync = useCallback(
    async (props: Tenant) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postTenantAsync,
    loading: isPending,
  }
}

export const usePutTenantMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Tenant) => putTenant(params),
  })

  const putTenantAsync = useCallback(
    async (props: Tenant) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putTenantAsync,
    loading: isPending,
  }
}

export const useDeleteTenantMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteTenant(id),
  })

  const deleteTenantAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteTenantAsync,
    loading: isPending,
  }
}

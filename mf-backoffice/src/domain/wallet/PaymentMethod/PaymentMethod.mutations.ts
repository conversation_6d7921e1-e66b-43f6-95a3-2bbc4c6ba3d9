import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  PaymentMethodListParams,
  PaymentMethod,
} from '@app/domain/wallet/PaymentMethod/PaymentMethod'
import {
  postPaymentMethod,
  putPaymentMethod,
  getPaymentMethods,
  getPaymentMethodById,
  deletePaymentMethod,
} from '@app/domain/wallet/PaymentMethod/PaymentMethod.api'

export const useGetPaymentMethodsQuery = (params: PaymentMethodListParams) => {
  const getPaymentMethodsFn = useQuery({
    queryKey: ['getPaymentMethods', params],
    queryFn: () => getPaymentMethods(params),
    enabled: params !== null,
  })

  return getPaymentMethodsFn
}

export const useGetPaymentMethodByIdQuery = (id: string) => {
  const getPaymentMethodByIdFn = useQuery({
    queryKey: ['getPaymentMethodById', id],
    queryFn: () => getPaymentMethodById(id),
    enabled: id !== null,
  })

  return getPaymentMethodByIdFn
}

export const usePostPaymentMethodMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PaymentMethod) => postPaymentMethod(params),
  })

  const postPaymentMethodAsync = useCallback(
    async (props: PaymentMethod) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postPaymentMethodAsync,
    loading: isPending,
  }
}

export const usePutPaymentMethodMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PaymentMethod) => putPaymentMethod(params),
  })

  const putPaymentMethodAsync = useCallback(
    async (props: PaymentMethod) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putPaymentMethodAsync,
    loading: isPending,
  }
}

export const useDeletePaymentMethodMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deletePaymentMethod(id),
  })

  const deletePaymentMethodAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deletePaymentMethodAsync,
    loading: isPending,
  }
}

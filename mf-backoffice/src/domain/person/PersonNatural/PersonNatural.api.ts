import api from '@app/domain/services/api'
import type { PersonNatural } from '@app/domain/person/PersonNatural/PersonNatural'

export const getPersonNaturals = async () => {
  const response = await api.get('/person/natural')

  return response?.data
}

export const getPersonNaturalById = async (id: string) => {
  const response = await api.get(`/person/natural/${id}`)

  return response?.data
}

export const getPersonNaturalByPersonId = async (personId: string) => {
  const response = await api.get(`/person/natural/personId/${personId}`)

  return response?.data
}

export const postPersonNatural = async (params: Omit<PersonNatural, 'id'>) => {
  const response = await api.post('/person/natural', params)

  return response?.data
}

export const putPersonNatural = async (params: PersonNatural) => {
  const { id, ...body } = params
  const response = await api.put(`/person/natural/${id}`, body)

  return response?.data
}

export const deletePersonNatural = async (id: string) => {
  const response = await api.delete(`/person/natural/${id}`)

  return response?.data
}

import api from '@app/domain/services/api'
import type { Contact } from '@app/domain/person/Contact/Contact'

export const getContacts = async () => {
  const response = await api.get('/person/contact')

  return response?.data
}

export const getContactById = async (id: string) => {
  const response = await api.get(`/person/contact/${id}`)

  return response?.data
}

export const postContact = async (params: Omit<Contact, 'id'>) => {
  const response = await api.post('/person/contact', params)

  return response?.data
}

export const putContact = async (params: Contact) => {
  const { id, ...body } = params
  const response = await api.put(`/person/contact/${id}`, body)

  return response?.data
}

export const deleteContact = async (id: string) => {
  const response = await api.delete(`/person/contact/${id}`)

  return response?.data
}

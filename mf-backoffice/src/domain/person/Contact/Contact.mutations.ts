import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { Contact } from '@app/domain/person/Contact/Contact'
import {
  postContact,
  putContact,
  getContacts,
  getContactById,
  deleteContact,
} from '@app/domain/person/Contact/Contact.api'

export const useGetContactsQuery = () => {
  const getContactsFn = useQuery({
    queryKey: ['getContacts'],
    queryFn: () => getContacts(),
  })

  return getContactsFn
}

export const useGetContactByIdQuery = (id: string) => {
  const getContactByIdFn = useQuery({
    queryKey: ['getContactById', id],
    queryFn: () => getContactById(id),
    enabled: id !== null,
  })

  return getContactByIdFn
}

export const usePostContactMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Contact) => postContact(params),
  })

  const postContactAsync = useCallback(
    async (props: Contact) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postContactAsync,
    loading: isPending,
  }
}

export const usePutContactMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Contact) => putContact(params),
  })

  const putContactAsync = useCallback(
    async (props: Contact) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putContactAsync,
    loading: isPending,
  }
}

export const useDeleteContactMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteContact(id),
  })

  const deleteContactAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteContactAsync,
    loading: isPending,
  }
}

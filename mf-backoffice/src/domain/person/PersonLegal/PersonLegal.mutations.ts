import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { PersonLegal } from '@app/domain/person/PersonLegal/PersonLegal'
import {
  postPersonLegal,
  putPersonLegal,
  getPersonLegals,
  getPersonLegalById,
  getPersonLegalByPersonId,
  deletePersonLegal,
} from '@app/domain/person/PersonLegal/PersonLegal.api'

export const useGetPersonLegalsQuery = () => {
  const getPersonLegalFn = useQuery({
    queryKey: ['getPersonLegals'],
    queryFn: () => getPersonLegals(),
  })

  return getPersonLegalFn
}

export const useGetPersonLegalByIdQuery = (id: string) => {
  const getPersonLegalByIdFn = useQuery({
    queryKey: ['getPersonLegalById', id],
    queryFn: () => getPersonLegalById(id),
    enabled: id !== null,
  })

  return getPersonLegalByIdFn
}

export const useGetPersonLegalByPersonIdQuery = (personId: string) => {
  const getPersonLegalByPersonIdFn = useQuery({
    queryKey: ['getPersonLegalByPersonId', personId],
    queryFn: () => getPersonLegalByPersonId(personId),
    enabled: personId !== null,
  })

  return getPersonLegalByPersonIdFn
}

export const usePostPersonLegalMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PersonLegal) => postPersonLegal(params),
  })

  const postPersonLegalAsync = useCallback(
    async (props: PersonLegal) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postPersonLegalAsync,
    loading: isPending,
  }
}

export const usePutPersonLegalMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PersonLegal) => putPersonLegal(params),
  })

  const putPersonLegalAsync = useCallback(
    async (props: PersonLegal) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putPersonLegalAsync,
    loading: isPending,
  }
}

export const useDeletePersonLegalMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deletePersonLegal(id),
  })

  const deletePersonLegalAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deletePersonLegalAsync,
    loading: isPending,
  }
}

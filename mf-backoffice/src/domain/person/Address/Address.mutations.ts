import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { Address } from '@app/domain/person/Address/Address'
import {
  postAddress,
  putAddress,
  getAddresses,
  getAddressById,
  deleteAddress,
} from '@app/domain/person/Address/Address.api'

export const useGetAddressesQuery = () => {
  const getAddressesFn = useQuery({
    queryKey: ['getAddresses'],
    queryFn: () => getAddresses(),
  })

  return getAddressesFn
}

export const useGetAddressByIdQuery = (id: string) => {
  const getAddressByIdFn = useQuery({
    queryKey: ['getAddressById', id],
    queryFn: () => getAddressById(id),
    enabled: id !== null,
  })

  return getAddressByIdFn
}

export const usePostAddressMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Address) => postAddress(params),
  })

  const postAddressAsync = useCallback(
    async (props: Address) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postAddressAsync,
    loading: isPending,
  }
}

export const usePutAddressMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Address) => putAddress(params),
  })

  const putAddressAsync = useCallback(
    async (props: Address) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putAddressAsync,
    loading: isPending,
  }
}

export const useDeleteAddressMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteAddress(id),
  })

  const deleteAddressAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteAddressAsync,
    loading: isPending,
  }
}

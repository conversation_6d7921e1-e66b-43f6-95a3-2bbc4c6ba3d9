import api from '@app/domain/services/api'
import type { Address } from '@app/domain/person/Address/Address'

export const getAddresses = async () => {
  const response = await api.get('/person/address')

  return response?.data
}

export const getAddressById = async (id: string) => {
  const response = await api.get(`/person/address/${id}`)

  return response?.data
}

export const postAddress = async (params: Omit<Address, 'id'>) => {
  const response = await api.post('/person/address', params)

  return response?.data
}

export const putAddress = async (params: Address) => {
  const { id, ...body } = params
  const response = await api.put(`/person/address/${id}`, body)

  return response?.data
}

export const deleteAddress = async (id: string) => {
  const response = await api.delete(`/person/address/${id}`)

  return response?.data
}

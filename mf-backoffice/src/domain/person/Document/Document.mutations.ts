import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { Document } from '@app/domain/person/Document/Document'
import {
  postDocument,
  putDocument,
  getDocuments,
  getDocumentById,
  deleteDocument,
} from '@app/domain/person/Document/Document.api'

export const useGetDocumentsQuery = () => {
  const getDocumentFn = useQuery({
    queryKey: ['getDocuments'],
    queryFn: () => getDocuments(),
  })

  return getDocumentFn
}

export const useGetDocumentByIdQuery = (id: string) => {
  const getDocumentByIdFn = useQuery({
    queryKey: ['getDocumentById', id],
    queryFn: () => getDocumentById(id),
    enabled: id !== null,
  })

  return getDocumentByIdFn
}

export const usePostDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Document) => postDocument(params),
  })

  const postDocumentAsync = useCallback(
    async (props: Document) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postDocumentAsync,
    loading: isPending,
  }
}

export const usePutDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Document) => putDocument(params),
  })

  const putDocumentAsync = useCallback(
    async (props: Document) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putDocumentAsync,
    loading: isPending,
  }
}

export const useDeleteDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteDocument(id),
  })

  const deleteDocumentAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteDocumentAsync,
    loading: isPending,
  }
}

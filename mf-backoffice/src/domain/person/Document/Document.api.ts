import api from '@app/domain/services/api'
import type { Document } from '@app/domain/person/Document/Document'

export const getDocuments = async () => {
  const response = await api.get('/person/document')

  return response?.data
}

export const getDocumentById = async (id: string) => {
  const response = await api.get(`/person/document/${id}`)

  return response?.data
}

export const postDocument = async (params: Omit<Document, 'id'>) => {
  const response = await api.post('/person/document', params)

  return response?.data
}

export const putDocument = async (params: Document) => {
  const { id, ...body } = params
  const response = await api.put(`/person/document/${id}`, body)

  return response?.data
}

export const deleteDocument = async (id: string) => {
  const response = await api.delete(`/person/document/${id}`)

  return response?.data
}

import { useQuery } from '@tanstack/react-query'

import { getPersons, getPersonById } from '@app/domain/person/Person/Person.api'

export const useGetPersonsQuery = () => {
  const getPersonsFn = useQuery({
    queryKey: ['getPersons'],
    queryFn: () => getPersons(),
  })

  return getPersonsFn
}

export const useGetPersonByIdQuery = (id: string) => {
  const getPersonByIdFn = useQuery({
    queryKey: ['getPersonById', id],
    queryFn: () => getPersonById(id),
    enabled: id !== null,
  })

  return getPersonByIdFn
}

import api from '@app/domain/services/api'
import type {
  StockLocation,
  StockLocationListParams,
} from '@app/domain/stock/StockLocation/StockLocation'

export const getStockLocations = async (params: StockLocationListParams) => {
  const response = await api.get(
    `/stock/stock-locations?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getStockLocationById = async (id: string) => {
  const response = await api.get(`/stock/stock-locations/${id}`)

  return response?.data
}

export const postStockLocation = async (params: Omit<StockLocation, 'id'>) => {
  const response = await api.post('/stock/stock-locations', params)

  return response?.data
}

export const putStockLocation = async (params: StockLocation) => {
  const { id, ...body } = params
  const response = await api.put(`/stock/stock-locations/${id}`, body)

  return response?.data
}

export const deleteStockLocation = async (id: string) => {
  const response = await api.delete(`/stock/stock-locations/${id}`)

  return response?.data
}

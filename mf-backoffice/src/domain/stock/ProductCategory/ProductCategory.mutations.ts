import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  ProductCategoryListParams,
  ProductCategory,
} from '@app/domain/stock/ProductCategory/ProductCategory'
import {
  postProductCategory,
  putProductCategory,
  getProductCategories,
  getProductCategoryById,
  deleteProductCategory,
} from '@app/domain/stock/ProductCategory/ProductCategory.api'

export const useGetProductCategoriesQuery = (
  params: ProductCategoryListParams,
) => {
  const getProductCategoriesFn = useQuery({
    queryKey: ['getProductCategories', params],
    queryFn: () => getProductCategories(params),
    enabled: params !== null,
  })

  return getProductCategoriesFn
}

export const useGetProductCategoryByIdQuery = (id: string) => {
  const getProductCategoryByIdFn = useQuery({
    queryKey: ['getProductCategoryById', id],
    queryFn: () => getProductCategoryById(id),
    enabled: id !== null,
  })

  return getProductCategoryByIdFn
}

export const usePostProductCategoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: ProductCategory) => postProductCategory(params),
  })

  const postProductCategoryAsync = useCallback(
    async (props: ProductCategory) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postProductCategoryAsync,
    loading: isPending,
  }
}

export const usePutProductCategoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: ProductCategory) => putProductCategory(params),
  })

  const putProductCategoryAsync = useCallback(
    async (props: ProductCategory) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putProductCategoryAsync,
    loading: isPending,
  }
}

export const useDeleteProductCategoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteProductCategory(id),
  })

  const deleteProductCategoryAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteProductCategoryAsync,
    loading: isPending,
  }
}

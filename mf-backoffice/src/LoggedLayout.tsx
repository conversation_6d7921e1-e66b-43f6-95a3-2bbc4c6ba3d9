import {
  LoggedPageWrap,
  ContentWrap,
} from '@thrift/design-system/packages/templates/LoggedPage'
import { Sidebar } from '@thrift/design-system/packages/organisms/Sidebar'
import { Navbar } from '@thrift/design-system/packages/molecules/Navbar'
import { MenuLateral } from '@thrift/design-system/packages/molecules/MenuLateral'
import { Outlet } from 'react-router-dom'

const LoggedLayout = () => {
  return (
    <LoggedPageWrap>
      <Navbar
        logo="Logo"
        menus={[
          { name: 'Dashboard', link: '/dashboard' },
          { name: 'Settings', link: '/settings' },
          { name: 'Profile', link: '/profile' },
        ]}
      />
      <Sidebar>
        <MenuLateral
          menus={[
            { name: 'Dashboard', link: '/dashboard', icon: 'home' },
            { name: 'Settings', link: '/settings', icon: 'settings' },
            { name: 'Profile', link: '/profile', icon: 'user' },
          ]}
        />
        <ContentWrap>
          <Outlet />
        </ContentWrap>
      </Sidebar>
    </LoggedPageWrap>
  )
}

export default LoggedLayout

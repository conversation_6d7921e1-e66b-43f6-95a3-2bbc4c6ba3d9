#!/bin/bash
set -eux

# Define variables
_working_dir='/usr/src/app'
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
_docker_compose_filename="${_root_path}/docker/macro-frontends/macro-frontend-docker-compose.yml"
_macro_services_filename="${_root_path}/environment/macro-frontends.list"

function _build() {
  docker compose --file ${_docker_compose_filename} pull macro-frontend-build < /dev/null

  while IFS= read -r _macroFrontend; do
    [ -z $_macroFrontend ] && continue
    docker compose \
      --project-name "thrift" \
      --file ${_docker_compose_filename} \
      run --rm --no-TTY \
      --name "${_macroFrontend}-build" \
      --env APP_NAME=${_macroFrontend} \
      macro-frontend-build < /dev/null
  done < "$_macro_services_filename"
}

function _cleanup() {
  docker compose \
    --project-name "thrift" \
    --file ${_docker_compose_filename} \
    run --rm --no-TTY \
    macro-frontend-cleanup < /dev/null
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --build)
      _build
      ;;

    --cleanup)
      _cleanup
      ;;
  esac
done

exit 0

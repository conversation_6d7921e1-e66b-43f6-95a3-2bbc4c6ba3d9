#!/bin/bash
set -eux

# Define variables
_working_dir='/usr/src/app'
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
_docker_compose_filename="${_root_path}/docker/api-gateway/api-gateway-docker-compose.yml"

function _build() {
  docker compose --file ${_docker_compose_filename} pull api-gateway-build < /dev/null

  for service in "${_root_path}/.cache/apps"/ms-*/; do
    [ ! -d "${service}" ] && continue

    local _macroService=$(basename "${service}")

    docker compose \
      --project-name "thrift" \
      --file ${_docker_compose_filename} \
      run --rm --detach --no-TTY \
      --volume "${service}:${_working_dir}" \
      --env APP_NAME=${_macroService} \
      api-gateway-build < /dev/null
  done
}

function _up() {
  docker compose --file ${_docker_compose_filename} pull api-gateway-runtime < /dev/null

  docker compose \
    --file ${_docker_compose_filename} \
    up --detach --build api-gateway-runtime < /dev/null
}

function _down() {
  local _containers_to_remove=$(docker ps -a --format "{{.ID}}\t{{.Names}}" | grep -E '\sapi-gateway-' | awk '{print $1}')

  if [ ! -z "$_containers_to_remove" ]; then
    docker compose \
      --file ${_docker_compose_filename} \
      kill api-gateway-runtime < /dev/null

    docker compose \
      --file ${_docker_compose_filename} \
      rm --force --volumes api-gateway-runtime < /dev/null
  fi
}

function _cleanup() {
  docker compose \
    --project-name "thrift" \
    --file ${_docker_compose_filename} \
    run --rm --no-TTY \
    api-gateway-cleanup < /dev/null
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --build)
      _build
      ;;

    --up)
      _up
      ;;

    --down)
      _down
      ;;

    --cleanup)
      _down
      _cleanup
      ;;
  esac
done

exit 0

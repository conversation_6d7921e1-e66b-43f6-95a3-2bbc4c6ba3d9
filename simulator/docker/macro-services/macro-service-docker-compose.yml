name: thrift
services:
  macro-service-build:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/github.env
      - ../variables/paths.env
    command: /bin/bash /usr/src/scripts/macro-service-build.sh
    volumes:
      - ./scripts:/usr/src/scripts
      - ../../.cache/apps:/usr/src/storage

  macro-service-runtime:
    image: node:22-alpine
    working_dir: /usr/src/app
    env_file:
      - ../variables/database.env
    command: /usr/local/bin/node /usr/src/app/dist/src/main.js

  macro-service-cleanup:
    container_name: macro-service-cleanup
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/paths.env
    volumes:
      - ./scripts:/usr/src/scripts
      - ../../.cache/apps:/usr/src/app
    command: /bin/bash /usr/src/scripts/macro-service-cleanup.sh

name: thrift
services:
  macro-frontend-build:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/github.env
      - ../variables/paths.env
    command: /bin/bash /usr/src/scripts/macro-frontend-build.sh
    volumes:
      - ./scripts:/usr/src/scripts
      - ../../.cache/apps:/usr/src/storage

  macro-frontend-cleanup:
    container_name: macro-frontend-cleanup
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/paths.env
    volumes:
      - ./scripts:/usr/src/scripts
      - ../../.cache/apps:/usr/src/app
    command: /bin/bash /usr/src/scripts/macro-frontend-cleanup.sh

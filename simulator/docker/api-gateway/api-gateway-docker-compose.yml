name: thrift
services:
  api-gateway-build:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/paths.env
    command: /bin/bash /usr/src/scripts/api-gateway-build.sh
    volumes:
      - ./scripts:/usr/src/scripts
      - ./scripts/templates:/usr/src/templates
      - ../../.cache/server/api-gateway:/usr/src/storage

  api-gateway-runtime:
    container_name: api-gateway-runtime
    image: nginx:1.28
    working_dir: /usr/src/app
    command: nginx -g 'daemon off;'
    volumes:
      - ./scripts/conf.d:/etc/nginx/conf.d
      - ../../.cache/server/api-gateway/includes:/etc/nginx/includes
    ports:
      - "8080:8080"

  api-gateway-cleanup:
    container_name: api-gateway-cleanup
    image: node:22-alpine
    working_dir: /usr/src/app
    volumes:
      - ../../.cache/server/api-gateway:/usr/src/app
    command: rm -rf ./includes

name: thrift
services:
  cdn-build:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/paths.env
    command: /bin/bash /usr/src/scripts/cdn-build.sh
    volumes:
      - ./scripts:/usr/src/scripts
      - ./scripts/templates:/usr/src/templates
      - ../../.cache/server/cdn:/usr/src/storage

  cdn-runtime:
    container_name: cdn-runtime
    image: nginx:1.28
    working_dir: /usr/src/app
    command: nginx -g 'daemon off;'
    volumes:
      - ./scripts/conf.d:/etc/nginx/conf.d
      - ../../.cache/server/cdn/includes:/etc/nginx/includes
      - ../../.cache/server/cdn/html:/etc/nginx/html/app
    ports:
      - "80:80"

  cdn-cleanup:
    container_name: cdn-cleanup
    image: node:22-alpine
    working_dir: /usr/src/app
    volumes:
      - ../../.cache/server/cdn:/usr/src/app
    command: rm -rf ./html ./includes
